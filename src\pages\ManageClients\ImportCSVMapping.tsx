import { But<PERSON> } from "@/components/ui/button";
import { Info, RefreshCw } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { SelectSheetInfoCard } from "@/components/common/SelectSheetInfoCard";
import { InputText } from "@/components/common/InputText/InputText";
import { useState, useEffect } from "react";
import {
	getFileHeaders,
	getFileSheets,
	autoMatchColumns,
	type FieldOption,
} from "@/utils/fileParser";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useBusinessAttributesForForm } from "@/hooks/useBusinessAttributes";
import type { BusinessAttribute } from "@/types/businessAttributes";

interface MappingRow {
	id: number;
	excel: string;
	migranium: string;
	type: string;
	isCreatingNew?: boolean;
	newFieldLabel?: string;
	isAutoMatched?: boolean;
	matchConfidence?: number;
}

interface MigraniumField {
	key: string;
	label: string;
	type: string;
}

interface CustomField {
	key: string;
	label: string;
	type: string;
}

interface ImportCSVMappingProps {
	uploadedFile: File | null;
	onBack: () => void;
	onNext: () => void;
	onTest: () => void;
	onMappingComplete?: (mappingData: {
		mappingRows: MappingRow[];
		selectedSheet: string;
		availableSheets: string[];
	}) => void;
}

export default function ImportCSVMapping({
	uploadedFile,
	onBack,
	onNext,
	onTest,
	onMappingComplete,
}: ImportCSVMappingProps) {
	// Fetch business attributes for form fields
	const { data: businessAttributesData, isLoading: isLoadingAttributes } =
		useBusinessAttributesForForm();
	const businessAttributes: BusinessAttribute[] =
		(businessAttributesData as any)?.data || [];

	// State management
	const [mappingRows, setMappingRows] = useState<MappingRow[]>([]);
	const [isLoadingHeaders, setIsLoadingHeaders] = useState(false);
	const [customFields, setCustomFields] = useState<CustomField[]>([]);
	const [availableSheets, setAvailableSheets] = useState<string[]>([]);
	const [selectedSheet, setSelectedSheet] = useState<string>("");
	const [isLoadingSheets, setIsLoadingSheets] = useState(false);

	// Available field types for new custom fields
	const fieldTypes = [
		{ value: "text", label: "Text" },
		{ value: "number", label: "Number" },
		{ value: "email", label: "Email" },
		{ value: "phone", label: "Phone" },
		{ value: "date", label: "Date" },
		{ value: "checkbox", label: "Checkbox" },
		{ value: "textarea", label: "Textarea" },
	];

	// Create available Migranium fields (basic + custom attributes + dynamically created fields)
	const availableMigraniumFields: MigraniumField[] = [
		{ key: "firstName", label: "First Name", type: "text" },
		{ key: "lastName", label: "Last Name", type: "text" },
		{ key: "email", label: "Email", type: "email" },
		{ key: "phone", label: "Phone", type: "phone" },
		...businessAttributes.map((attr) => ({
			key: attr.key,
			label: attr.label,
			type: attr.type,
		})),
		...customFields.map((field) => ({
			key: field.key,
			label: field.label,
			type: field.type,
		})),
	];

	// Load sheets when file is uploaded
	useEffect(() => {
		const loadFileSheets = async () => {
			if (!uploadedFile) {
				setAvailableSheets([]);
				setSelectedSheet("");
				return;
			}

			setIsLoadingSheets(true);
			try {
				const sheets = await getFileSheets(uploadedFile);
				setAvailableSheets(sheets);

				// Auto-select first sheet if available
				if (sheets.length > 0) {
					setSelectedSheet(sheets[0]);
				} else {
					setSelectedSheet("");
				}
			} catch (error) {
				console.error("Error loading file sheets:", error);
				setAvailableSheets([]);
				setSelectedSheet("");
			} finally {
				setIsLoadingSheets(false);
			}
		};

		loadFileSheets();
	}, [uploadedFile]);

	// Auto-populate headers when file is uploaded or sheet is selected
	useEffect(() => {
		const loadFileHeaders = async () => {
			if (!uploadedFile || isLoadingAttributes) return;

			setIsLoadingHeaders(true);
			try {
				const headers = await getFileHeaders(
					uploadedFile,
					selectedSheet || undefined
				);

				if (headers.length > 0) {
					// Prepare available fields for auto-matching
					const availableFields: FieldOption[] = [
						{ key: "firstName", label: "First Name", type: "text" },
						{ key: "lastName", label: "Last Name", type: "text" },
						{ key: "email", label: "Email", type: "email" },
						{ key: "phone", label: "Phone", type: "phone" },
						...businessAttributes.map((attr) => ({
							key: attr.key,
							label: attr.label,
							type: attr.type,
						})),
						...customFields.map((field) => ({
							key: field.key,
							label: field.label,
							type: field.type,
						})),
					];

					// Perform automatic matching
					const autoMatchResults = autoMatchColumns(
						headers,
						availableFields
					);

					// Create mapping rows with automatic matches
					const newMappingRows: MappingRow[] = autoMatchResults.map(
						(result, index) => ({
							id: index + 1,
							excel: result.csvHeader,
							migranium: result.matchedField?.key || "", // Auto-matched field or empty
							type: result.matchedField?.type || "text", // Auto-matched type or default
							isCreatingNew: false,
							newFieldLabel: "",
							isAutoMatched: !!result.matchedField,
							matchConfidence: result.confidence,
						})
					);
					setMappingRows(newMappingRows);
				}
			} catch (error) {
				console.error("Error loading file headers:", error);
			} finally {
				setIsLoadingHeaders(false);
			}
		};

		loadFileHeaders();
	}, [
		uploadedFile,
		selectedSheet,
		businessAttributes,
		customFields,
		isLoadingAttributes,
	]);

	const handleExcelColumnChange = (id: number, value: string) => {
		setMappingRows((prev) =>
			prev.map((row) => (row.id === id ? { ...row, excel: value } : row))
		);
	};

	const handleMigraniumFieldChange = (id: number, value: string) => {
		if (value === "CREATE_NEW") {
			// Enable create new mode for this row
			setMappingRows((prev) =>
				prev.map((row) =>
					row.id === id
						? {
								...row,
								migranium: "",
								type: "text",
								isCreatingNew: true,
								newFieldLabel: "",
								isAutoMatched: false,
								matchConfidence: 0,
							}
						: row
				)
			);
		} else {
			const selectedField = availableMigraniumFields.find(
				(field) => field.key === value
			);
			setMappingRows((prev) =>
				prev.map((row) =>
					row.id === id
						? {
								...row,
								migranium: value,
								type: selectedField?.type || "text",
								isCreatingNew: false,
								newFieldLabel: "",
								isAutoMatched: false, // Clear auto-match flag when manually changed
								matchConfidence: 0,
							}
						: row
				)
			);
		}
	};

	const handleNewFieldLabelChange = (id: number, label: string) => {
		setMappingRows((prev) =>
			prev.map((row) =>
				row.id === id ? { ...row, newFieldLabel: label } : row
			)
		);
	};

	const handleNewFieldTypeChange = (id: number, type: string) => {
		setMappingRows((prev) =>
			prev.map((row) => {
				if (row.id === id) {
					// If this is a custom field that's already created, update the customFields array too
					if (row.migranium && row.migranium.startsWith("custom_")) {
						setCustomFields((prevCustomFields) =>
							prevCustomFields.map((field) =>
								field.key === row.migranium
									? { ...field, type }
									: field
							)
						);
					}
					return { ...row, type };
				}
				return row;
			})
		);
	};

	const handleCreateNewField = (id: number) => {
		const row = mappingRows.find((r) => r.id === id);
		if (!row || !row.newFieldLabel) return;

		// Generate a unique key for the new field
		const fieldKey = `custom_${Date.now()}_${row.newFieldLabel
			.toLowerCase()
			.replace(/\s+/g, "_")}`;

		// Add to custom fields
		const newCustomField: CustomField = {
			key: fieldKey,
			label: row.newFieldLabel,
			type: row.type,
		};

		// Update both states - React will batch these updates
		setCustomFields((prev) => [...prev, newCustomField]);
		setMappingRows((prev) =>
			prev.map((r) =>
				r.id === id
					? {
							...r,
							migranium: fieldKey,
							isCreatingNew: false,
							newFieldLabel: "",
							isAutoMatched: false, // Clear auto-match flag when creating new field
							matchConfidence: 0,
						}
					: r
			)
		);
	};

	// Get available options for a specific row (excluding already selected ones)
	const getAvailableOptions = (currentRowId: number) => {
		const currentRow = mappingRows.find((row) => row.id === currentRowId);
		const selectedValues = mappingRows
			.filter((row) => row.id !== currentRowId && row.migranium)
			.map((row) => row.migranium);

		let availableFields = availableMigraniumFields.filter(
			(field) => !selectedValues.includes(field.key)
		);

		// Ensure the currently selected field for this row is always included
		if (
			currentRow?.migranium &&
			!availableFields.find((f) => f.key === currentRow.migranium)
		) {
			// Find the field in all available fields (including custom fields)
			const currentField = availableMigraniumFields.find(
				(f) => f.key === currentRow.migranium
			);
			if (currentField) {
				availableFields = [currentField, ...availableFields];
			}
		}

		// Always add "Create New" option at the end
		return [
			...availableFields,
			{ key: "CREATE_NEW", label: "Create New Field", type: "text" },
		];
	};

	const handleRefreshHeaders = async () => {
		if (!uploadedFile) return;

		setIsLoadingHeaders(true);
		try {
			const headers = await getFileHeaders(
				uploadedFile,
				selectedSheet || undefined
			);

			if (headers.length > 0) {
				// Prepare available fields for auto-matching
				const availableFields: FieldOption[] = [
					{ key: "firstName", label: "First Name", type: "text" },
					{ key: "lastName", label: "Last Name", type: "text" },
					{ key: "email", label: "Email", type: "email" },
					{ key: "phone", label: "Phone", type: "phone" },
					...businessAttributes.map((attr) => ({
						key: attr.key,
						label: attr.label,
						type: attr.type,
					})),
					...customFields.map((field) => ({
						key: field.key,
						label: field.label,
						type: field.type,
					})),
				];

				// Perform automatic matching
				const autoMatchResults = autoMatchColumns(
					headers,
					availableFields
				);

				// Create new mapping rows with automatic matches
				const newMappingRows: MappingRow[] = autoMatchResults.map(
					(result, index) => ({
						id: index + 1,
						excel: result.csvHeader,
						migranium: result.matchedField?.key || "", // Auto-matched field or empty
						type: result.matchedField?.type || "text", // Auto-matched type or default
						isCreatingNew: false,
						newFieldLabel: "",
						isAutoMatched: !!result.matchedField,
						matchConfidence: result.confidence,
					})
				);
				setMappingRows(newMappingRows);
			}
		} catch (error) {
			console.error("Error refreshing file headers:", error);
		} finally {
			setIsLoadingHeaders(false);
		}
	};

	const handleSheetSelect = (sheet: string) => {
		setSelectedSheet(sheet);
		// Headers will be automatically reloaded due to the useEffect dependency
	};

	const handleNext = () => {
		// Pass mapping data to parent before proceeding
		if (onMappingComplete) {
			onMappingComplete({
				mappingRows,
				selectedSheet,
				availableSheets,
			});
		}
		onNext();
	};
	return (
		<div className="flex w-full flex-col items-start justify-start gap-2">
			{uploadedFile && (
				<div className="flex w-full items-center justify-between rounded-lg bg-blue-50 p-3">
					<div className="flex items-center gap-2">
						<div className="text-sm font-medium text-blue-900">
							Mapping columns for: {uploadedFile.name}
							{selectedSheet && availableSheets.length > 0 && (
								<span className="text-blue-700">
									{" "}
									- Sheet: {selectedSheet}
								</span>
							)}
						</div>
					</div>
					<div className="text-xs text-blue-600">
						{isLoadingSheets
							? "Loading sheets..."
							: isLoadingHeaders
								? "Scanning headers..."
								: "Headers loaded"}
					</div>
				</div>
			)}
			<SelectSheetInfoCard
				onNext={onNext}
				selectedSheet={selectedSheet || "Select a sheet"}
				sheets={availableSheets}
				onSheetSelect={handleSheetSelect}
			/>

			<div className="flex flex-col items-start justify-start self-stretch rounded-2xl p-2">
				<div className="inline-flex items-center justify-start gap-3 self-stretch pb-2">
					<div className="flex min-w-20 flex-1 items-center justify-start gap-2.5">
						<div className="flex-1 justify-center text-xs leading-none font-medium text-gray-500">
							Column Titles from Excel
						</div>
						{uploadedFile && (
							<Button
								variant="ghost"
								size="sm"
								onClick={handleRefreshHeaders}
								disabled={isLoadingHeaders}
								className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
							>
								<RefreshCw
									className={`h-3 w-3 ${isLoadingHeaders ? "animate-spin" : ""}`}
								/>
							</Button>
						)}
					</div>
					<div className="flex min-w-20 flex-1 items-center justify-start gap-2.5">
						<div className="flex-1 justify-center text-xs leading-none font-medium text-gray-500">
							Titles on Migranium
						</div>
					</div>
					<div className="flex min-w-20 flex-1 items-center justify-start gap-2.5">
						<div className="flex-1 justify-center text-xs leading-none font-medium text-gray-500">
							Title Type
						</div>
					</div>
					<div className="inline-flex w-28 min-w-20 flex-col items-start justify-center gap-0.5">
						<div className="inline-flex items-center justify-start gap-0.5">
							<div className="justify-center text-xs leading-none font-medium text-gray-500">
								Validator
							</div>
							<Info className="h-3 w-3 text-gray-500" />
						</div>
						<div className="w-24 justify-start text-[8px] leading-3 font-normal text-gray-400">
							(Select two)
						</div>
					</div>
				</div>

				{mappingRows.length === 0 ? (
					<div className="flex items-center justify-center py-8 text-gray-500">
						<div className="text-center">
							<p className="text-sm">
								{uploadedFile
									? "Loading CSV headers..."
									: "Upload a CSV file to start mapping columns"}
							</p>
						</div>
					</div>
				) : (
					mappingRows.map((row) => (
						<div
							key={row.id}
							className="inline-flex items-center justify-start gap-3 self-stretch border-t border-gray-200 py-2"
						>
							<div className="inline-flex flex-1 flex-col items-start justify-start gap-2">
								<div className="flex flex-col items-start justify-start gap-2 self-stretch">
									<InputText
										value={row.excel}
										onChange={(e) =>
											handleExcelColumnChange(
												row.id,
												e.target.value
											)
										}
										size="sm"
										className="h-9 border-gray-300 bg-white text-xs font-normal text-gray-900 focus:outline-none"
										placeholder={
											isLoadingHeaders
												? "Loading headers..."
												: "Enter column title"
										}
										disabled={isLoadingHeaders}
									/>
								</div>
							</div>

							<div className="inline-flex flex-1 flex-col items-start justify-start gap-2">
								{row.isCreatingNew ? (
									<div className="flex w-full items-start gap-1">
										<Select
											value="CREATE_NEW"
											onValueChange={(value) => {
												if (value !== "CREATE_NEW") {
													handleMigraniumFieldChange(
														row.id,
														value as string
													);
												}
											}}
											disabled={isLoadingAttributes}
										>
											<SelectTrigger className="h-9 w-28 text-xs">
												<SelectValue placeholder="New" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="CREATE_NEW">
													New
												</SelectItem>
												{getAvailableOptions(row.id)
													.filter(
														(field) =>
															field.key !==
															"CREATE_NEW"
													)
													.map((field) => (
														<SelectItem
															key={field.key}
															value={field.key}
														>
															{field.label}
														</SelectItem>
													))}
											</SelectContent>
										</Select>
										<InputText
											value={row.newFieldLabel || ""}
											onChange={(e) =>
												handleNewFieldLabelChange(
													row.id,
													e.target.value
												)
											}
											onBlur={() => {
												if (row.newFieldLabel?.trim()) {
													handleCreateNewField(
														row.id
													);
												}
											}}
											size="sm"
											className="h-9 flex-1 border-gray-300 bg-white text-xs font-normal text-gray-900 focus:outline-none"
											placeholder="Enter Title"
										/>
									</div>
								) : (
									<Select
										key={`${row.id}-${row.migranium}-${customFields.length}`}
										value={row.migranium}
										onValueChange={(value) =>
											handleMigraniumFieldChange(
												row.id,
												value as string
											)
										}
										disabled={isLoadingAttributes}
									>
										<SelectTrigger className="h-9 w-full text-xs">
											<SelectValue
												placeholder={
													isLoadingAttributes
														? "Loading fields..."
														: "Select Migranium field"
												}
											/>
										</SelectTrigger>
										<SelectContent>
											{getAvailableOptions(row.id).map(
												(field) => (
													<SelectItem
														key={field.key}
														value={field.key}
													>
														{field.label}
													</SelectItem>
												)
											)}
										</SelectContent>
									</Select>
								)}
							</div>

							<div className="inline-flex flex-1 flex-col items-start justify-start gap-2">
								{row.isCreatingNew ||
								(row.migranium &&
									row.migranium.startsWith("custom_")) ? (
									<Select
										value={row.type}
										onValueChange={(value) =>
											handleNewFieldTypeChange(
												row.id,
												value as string
											)
										}
									>
										<SelectTrigger className="h-9 w-full text-xs">
											<SelectValue placeholder="Select Type" />
										</SelectTrigger>
										<SelectContent>
											{fieldTypes.map((type) => (
												<SelectItem
													key={type.value}
													value={type.value}
												>
													{type.label}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								) : (
									<div className="inline-flex h-9 items-center justify-start self-stretch overflow-hidden rounded-md bg-gray-50 px-3 py-2 outline-1 outline-offset-[-1px] outline-gray-300">
										<div className="flex-1 justify-start text-xs leading-none font-normal text-gray-600 capitalize">
											{row.type || "Select field first"}
										</div>
									</div>
								)}
							</div>

							<div className="flex w-28 items-center justify-start gap-1 overflow-hidden py-2">
								<div className="flex items-start justify-start gap-2">
									<div className="flex items-center justify-start gap-1.5">
										<Switch />
										<div className="h-5 w-5 justify-center text-xs leading-none font-normal text-gray-500">
											Off
										</div>
									</div>
								</div>
							</div>
						</div>
					))
				)}
			</div>

			<div className="flex w-full items-center justify-end gap-3 px-6 py-3">
				<Button
					onClick={onBack}
					variant="ghost"
					className="flex h-9 items-center justify-center gap-2 rounded-md px-4 py-2"
				>
					<span className="text-xs leading-none font-medium text-gray-900">
						Back
					</span>
				</Button>

				<div className="flex flex-1 items-center justify-end gap-3">
					<Button
						onClick={onTest}
						variant="outline"
						className="flex h-9 items-center justify-center gap-2 rounded-md bg-white px-4 py-2 outline-1 outline-offset-[-1px] outline-[#005893]"
					>
						<span className="text-xs leading-none font-medium text-[#005893]">
							Test
						</span>
					</Button>

					<Button
						onClick={handleNext}
						className="flex h-9 items-center justify-center gap-2 rounded-md bg-[#005893] px-4 py-2 hover:bg-[#004a7a]"
					>
						<span className="text-xs leading-none font-medium text-white">
							Next
						</span>
					</Button>
				</div>
			</div>
		</div>
	);
}
