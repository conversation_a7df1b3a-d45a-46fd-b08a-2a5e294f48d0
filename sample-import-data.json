{"metadata": {"fileName": "patients.csv", "selectedSheet": "", "totalRows": 3, "validRows": 3, "invalidRows": 0, "importTimestamp": "2024-01-15T10:30:00.000Z"}, "fieldMapping": {"First Name": {"migraniumField": "firstName", "fieldType": "text"}, "Last Name": {"migraniumField": "lastName", "fieldType": "text"}, "Email Address": {"migraniumField": "email", "fieldType": "email"}, "Phone Number": {"migraniumField": "phone", "fieldType": "phone"}, "Date of Birth": {"migraniumField": "custom_1642234567890_date_of_birth", "fieldType": "date"}}, "patients": [{"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "555-1234", "custom_1642234567890_date_of_birth": "1990-01-01"}, {"firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "555-5678", "custom_1642234567890_date_of_birth": "1985-05-15"}, {"firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "phone": "555-9012", "custom_1642234567890_date_of_birth": "1992-12-25"}]}